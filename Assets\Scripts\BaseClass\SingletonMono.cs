using UnityEngine;

/// <summary>
/// 单例基类，继承自MonoBehaviour。类名称写法：public class ScenesManager : SingletonMono<ScenesManager>
/// 只能实现在挂载到GameObject上，并且不能手动挂载。否则会导致场中同时存在多个相同实例。
/// 通过调用的方式来实现自动挂载。
/// </summary>

public class SingletonMono<T> : MonoBehaviour where T : MonoBehaviour
{
    private static T _instance;
    public static T Instance
    {
        get
        {
            if (_instance == null) //新建GameObject将脚本挂载到对象中，并将对象名称修改为类型名。
            {
                GameObject singleton = new GameObject();
                _instance = singleton.AddComponent<T>();
                singleton.name = typeof(T).ToString();

                DontDestroyOnLoad(singleton); // 切换场景不销毁该脚本。
            }
            return _instance;
        }
    }
}
