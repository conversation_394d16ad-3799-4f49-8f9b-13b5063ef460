using TMPro;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public interface INodeHandler { void HandleNode(DialogueNodeSO node); } // 节点处理器接口

public class DialoguePlayer : MonoBehaviour
{
    [Header("对话")]
    public GameObject dialogObj;
    public TextMeshProUGUI dialogShow;
    public GameObject dialogLeft;
    public GameObject leftName;
    public GameObject dialogRight;
    public GameObject rightName;
    public GameObject imagePanel;
    public GameObject nextBtn;

    [Header("分支")]
    public GameObject branObj;
    public Button branBtn;

    [Header("旁白")]
    public GameObject NarraObj;
    public TextMeshProUGUI NarraShow;

    [Header("呼喊")]
    public GameObject shoutObj;
    public TextMeshProUGUI shoutShow;
    public Image roleImage;

    [Header("等待传入")]
    public DialogueTreeSO dialogueTreeID;

    private Dictionary<string, Dictionary<string, object>> dialDict; // 角色字典
    private NodeHandlerFactory handlerFactory; // 节点处理器工厂
    private Queue<string> dialogueQueue = new Queue<string>(); // 对话队列
    private bool isPlayingDialogue = false; // 是否正在播放对话

    void Start()
    {
        handlerFactory = new NodeHandlerFactory(this); // 初始化节点处理器工厂
        EventCenter.Instance.AddEvent<string>("DialogueEvents", LoadDialogueTree); // 注册事件监听
    }

    void LoadDialogueTree(string dialogueName) // 加载对话树
    {
        if (isPlayingDialogue)
        {
            dialogueQueue.Enqueue(dialogueName);
            return;
        }

        isPlayingDialogue = true;
        DialogueTreeSO dialogueTree = Resources.Load<DialogueTreeSO>($"Data/Dialogue/{dialogueName}");
        if (dialogueTree != null)
        {
            dialogueTreeID = dialogueTree;
            StartDialogue(dialogueName);
        }
        else
        {
            Debug.LogError($"无法加载对话树: {dialogueName}");
            isPlayingDialogue = false;
            CheckQueue();
        }
    }

    void StartDialogue(string dialogueName) // 开始对话
    {
        InitializeDialDict();
        InitializeDialogBox();


        if (dialogueTreeID?.allNodes.OfType<NodeRoot>().FirstOrDefault() is NodeRoot nodeRoot)
        {
            var childNode = (DialogueNodeSO)nodeRoot.GetType().GetField("childNode")?.GetValue(nodeRoot);
            if (childNode != null)
            {
                NodeMethods(childNode);
            }
        }
    }

    private void InitializeDialDict() // 初始化角色字典
    {
        dialDict = dialogueTreeID?.roleLists
            .Where(roleList => roleList.roles != null)
            .ToDictionary(
                roleList => roleList.roles.roleID,
                roleList => new Dictionary<string, object>
                {
                    { "roleImage", roleList.roleImage },
                    { "leftRight", roleList.leftRight },
                    { "exist", roleList.exist },
                    { "name", GetTextFromID(roleList.roles.roleID) }
                }
            ) ?? new Dictionary<string, Dictionary<string, object>>();
    }

    public string GetTextFromID(string textID) // 获取文本内容
    {
        if (BootSetting.languageData?.TryGetValue(textID, out var values) == true && values.Count > 1) //这里和 BootSetting 捆绑了，后续需要解耦。
        {
            return values[1];
        }
        Debug.LogError($"无法找到ID: {textID}的文本内容");
        return string.Empty;
    }

    public void NodeMethods(DialogueNodeSO childNode) // 节点处理方法
    {
        Initialize();
        var handler = handlerFactory.CreateHandler(childNode);
        handler?.HandleNode(childNode);
    }

    public void Initialize() // 初始化游戏对象
    {
        if (dialogObj != null) dialogObj.SetActive(false);
        if (NarraObj != null) NarraObj.SetActive(false);
        if (shoutObj != null) shoutObj.SetActive(false);
    }

    public void CheckQueue() // 检查对话队列是否为空，否则开始下一条对话任务。
    {
        if (dialogueQueue.Count > 0)
        {
            string nextDialogue = dialogueQueue.Dequeue();
            LoadDialogueTree(nextDialogue);
        }
    }

    public void EndDialogue() // 结束节点
    {
        isPlayingDialogue = false; // 标记对话结束
        dialogueTreeID = null; // 清空对话树
        CheckQueue();
    }

    public void InitializeDialogBox() // 初始化对话框
    {
        if (dialogueTreeID?.roleLists == null || dialogueTreeID.roleLists.Length == 0)
            return;

        // 处理第一个角色
        var firstRole = dialogueTreeID.roleLists[0];
        ProcessRoleElement(firstRole);

        // 如果存在第二个角色则处理
        if (dialogueTreeID.roleLists.Length > 1)
        {
            var secondRole = dialogueTreeID.roleLists[1];
            ProcessRoleElement(secondRole);
        }
    }

    private void ProcessRoleElement(DialogueTreeSO.RoleList roleElement) // 处理初始化角色元素
    {
        // 如果不是左右位置，直接返回
        if (roleElement.leftRight == DialogueTreeSO.RoleList.LeftRight.Middle) return;

        // 使用三元运算符选择对象
        var dialogSide = roleElement.leftRight == DialogueTreeSO.RoleList.LeftRight.Left ? dialogLeft : dialogRight;
        var nameSide = roleElement.leftRight == DialogueTreeSO.RoleList.LeftRight.Left ? leftName : rightName;

        // 设置显示/隐藏状态
        dialogSide.SetActive(roleElement.exist);
        nameSide.SetActive(roleElement.exist);

        // 仅在exist为true时处理图片和名称
        if (roleElement.exist && roleElement.roles != null)
        {
            dialogSide.GetComponent<Image>().sprite = roleElement.roleImage;
            nameSide.GetComponentInChildren<TextMeshProUGUI>().text = GetTextFromID(roleElement.roles.roleID);
        }
    }
}
