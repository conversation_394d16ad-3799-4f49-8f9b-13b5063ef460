using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEngine;
using UnityEngine.Audio;

// 主要干了三个事，设置参数，音量调整，语言文本。
// 后续增加版本迭代。

public class BootSetting : MonoBehaviour
{
    [Header("游戏配置启动加载")]
    private string configPath; // 配置文件路径
    private string defaultConfigPath; // 默认配置文件路径
    public static JObject configData; // 配置数据
    private AudioMixer audioMixer; // 音频混合器

    void Awake()
    {
        configPath = Path.Combine(Application.persistentDataPath, "Configuration.Json"); // 设置配置文件路径
        defaultConfigPath = Path.Combine(Application.streamingAssetsPath, "DefaultConfiguration.Json"); // 设置默认配置文件路径
        EventCenter.Instance.AddEvent("OnResetProfile", Awake); // 添加重置配置事件

        if (File.Exists(configPath))
        {
            LoadConfig(configPath); // 加载配置文件
        }
        else
        {
            LoadConfig(defaultConfigPath); // 加载默认配置文件
            SaveConfig(); // 保存配置文件
        }

        // 初始化语言管理器
        LanguageManager.Instance.Initialize(configData);
        LoadAudioMixer(); // 加载音频混合器
    }

    void Start()
    {
        SetVolumeFromConfig(); // 从配置文件中设置音量
    }

    void LoadConfig(string path)
    {
        string jsonData = File.ReadAllText(path); // 读取配置文件内容
        configData = JObject.Parse(jsonData); // 解析JSON数据
    }

    void SaveConfig()
    {
        string jsonData = configData.ToString(); // 将配置数据转换为JSON字符串
        File.WriteAllText(configPath, jsonData); // 将JSON字符串写入配置文件
    }

    void LoadAudioMixer()
    {
        audioMixer = Resources.Load<AudioMixer>("Audio/AudioMixer"); // 加载音频混合器
    }

    void SetVolumeFromConfig()
    {
        string[] volumeParameters = { "Master", "BGM", "SFX", "Voice", "Menu" }; // 音量参数数组

        foreach (string param in volumeParameters)
        {
            if (configData[param] != null)
            {
                float volume = (float)configData[param]; // 获取配置文件中的音量设置
                audioMixer.SetFloat(param, volume); // 设置音频混合器的音量
            }
        }
    }

    void OnDestroy()
    {
        EventCenter.Instance.RemoveEvent("OnResetProfile", Awake);
    }
}
